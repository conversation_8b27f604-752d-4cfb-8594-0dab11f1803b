'use client'

import { useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'
import { OrderItem, Order } from '@/components/order-item'
import { Button } from '@/components/ui/button'

export default function OrdersPage() {
  // User state is kept for future order filtering functionality
  const [, setUser] = useState<{ id: string } | null>(null)
  const [loading, setLoading] = useState(true)
  const [orders, setOrders] = useState<Order[]>([])
  
  const supabase = createClient()
  const router = useRouter()
  
  useEffect(() => {
    const getUser = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error || !session) {
        router.push('/login')
        return
      }
      
      setUser(session.user)
      
      // Fetch real orders from the database
      try {
        const response = await fetch('/api/user/orders')
        if (response.ok) {
          const userOrders = await response.json()
          setOrders(userOrders)
        } else {
          console.error('Failed to fetch orders')
          setOrders([])
        }
      } catch (error) {
        console.error('Error fetching orders:', error)
        setOrders([])
      }
      
      setLoading(false)
    }
    
    getUser()
  }, [supabase, router])
  
  if (loading) {
    return <div className="text-center text-white">Loading...</div>
  }
  
  return (
    <div className="max-w-3xl mx-auto">
      <h2 className="text-xl font-medium mb-6 text-white">Order History</h2>
      
      {orders.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-400 mb-6">You haven&apos;t placed any orders yet.</p>
          <Button
            onClick={() => router.push('/collections')}
            className="bg-white hover:bg-gray-200 text-black rounded-none font-medium py-2 px-4"
          >
            Browse Products
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {orders.map((order) => (
            <OrderItem key={order.id} order={order} />
          ))}
        </div>
      )}
    </div>
  )
}