"use client";

import { useAuth } from '@/context/auth-context';
import { Button } from '@/components/ui/button';
import { useState } from 'react';

export function LogoutTest() {
  const { user, signOut } = useAuth();
  const [testResult, setTestResult] = useState<string>('');

  const testLogout = async () => {
    try {
      setTestResult('Testing logout...');
      console.log('Test: Starting logout');
      
      await signOut();
      
      setTestResult('Logout completed successfully');
      console.log('Test: Logout completed');
    } catch (error) {
      setTestResult(`Logout failed: ${error}`);
      console.error('Test: Logout failed:', error);
    }
  };

  // Only show in development and when user is logged in
  if (process.env.NODE_ENV !== 'development' || !user) return null;

  return (
    <div className="fixed bottom-20 right-4 bg-blue-900 border border-blue-600 p-4 text-xs text-white max-w-sm z-50">
      <h3 className="font-bold mb-2">Logout Test</h3>
      <Button 
        onClick={testLogout}
        size="sm"
        className="mb-2 w-full"
      >
        Test Logout
      </Button>
      {testResult && (
        <div className="text-xs text-blue-200">
          {testResult}
        </div>
      )}
    </div>
  );
}
