'use client'

import { AuthCustomForm } from '@/components/auth-custom-form'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { createClient } from '@/utils/supabase/client'

export default function LoginPage() {
  const router = useRouter()
  const supabase = createClient()
  const [session, setSession] = useState<{ user: { id: string; email?: string } } | null>(null)

  useEffect(() => {
    const getSession = async () => {
      const { data } = await supabase.auth.getSession()
      setSession(data.session)
    }

    getSession()

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session)
      if (session) {
        router.push('/')
        router.refresh()
      }
    })

    return () => subscription.unsubscribe()
  }, [router, supabase])

  // If user is already logged in, redirect to home page
  if (session) {
    router.push('/')
    return null
  }

  return (
    <div className="flex justify-center items-center min-h-screen p-4 bg-black">
      <div className="w-full max-w-md bg-neutral-900 border border-neutral-800 rounded-none p-8">
        <h1 className="text-2xl font-bold mb-6 text-center text-white">ACCOUNT</h1>
        <AuthCustomForm />
      </div>
    </div>
  )
}