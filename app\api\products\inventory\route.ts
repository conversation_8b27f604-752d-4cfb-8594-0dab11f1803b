import { NextRequest, NextResponse } from 'next/server';
import { getProductInventory } from '@/src/db/inventory';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const productName = searchParams.get('productName');
    
    if (!productName) {
      return NextResponse.json({ error: 'Product name is required' }, { status: 400 });
    }

    const inventory = await getProductInventory(productName);
    return NextResponse.json({ inventory });
  } catch (error) {
    console.error('Error in inventory API:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch inventory', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 });
  }
}