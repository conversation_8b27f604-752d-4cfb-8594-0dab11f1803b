'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { createClient } from '@/utils/supabase/client'

export default function PasswordSettings() {
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  
  const supabase = createClient()
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!currentPassword) {
      newErrors.currentPassword = 'Current password is required'
    }
    
    if (!newPassword) {
      newErrors.newPassword = 'New password is required'
    } else if (newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters'
    }
    
    if (newPassword !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)
    
    if (!validateForm()) return
    
    try {
      setLoading(true)
      
      // First, verify the current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: (await supabase.auth.getUser()).data.user?.email || '',
        password: currentPassword
      })
      
      if (signInError) {
        setMessage({ type: 'error', text: 'Current password is incorrect' })
        setLoading(false)
        return
      }
      
      // Change the password
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })
      
      if (error) throw error
      
      // Clear form
      setCurrentPassword('')
      setNewPassword('')
      setConfirmPassword('')
      
      setMessage({ type: 'success', text: 'Password updated successfully' })
    } catch (error: unknown) {
      setMessage({ type: 'error', text: error instanceof Error ? error.message : 'Error updating password' })
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <div className="max-w-xl mx-auto">
      <h2 className="text-xl font-medium mb-6 text-white">Change Password</h2>
      
      {message && (
        <div 
          className={`p-3 mb-6 ${
            message.type === 'success' ? 'bg-green-900/30 border border-green-800 text-green-200' : 
                                        'bg-red-900/30 border border-red-800 text-red-200'
          }`}
        >
          {message.text}
        </div>
      )}
      
      <form onSubmit={handlePasswordChange} className="space-y-6">
        <div>
          <Label htmlFor="currentPassword" className="text-white">Current Password</Label>
          <Input
            id="currentPassword"
            type="password"
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
              errors.currentPassword ? 'border-red-500' : ''
            }`}
          />
          {errors.currentPassword && (
            <p className="mt-1 text-xs text-red-400">{errors.currentPassword}</p>
          )}
        </div>
        
        <div>
          <Label htmlFor="newPassword" className="text-white">New Password</Label>
          <Input
            id="newPassword"
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
              errors.newPassword ? 'border-red-500' : ''
            }`}
          />
          {errors.newPassword && (
            <p className="mt-1 text-xs text-red-400">{errors.newPassword}</p>
          )}
          <p className="mt-1 text-xs text-neutral-400">Password must be at least 8 characters</p>
        </div>
        
        <div>
          <Label htmlFor="confirmPassword" className="text-white">Confirm New Password</Label>
          <Input
            id="confirmPassword"
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className={`mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none ${
              errors.confirmPassword ? 'border-red-500' : ''
            }`}
          />
          {errors.confirmPassword && (
            <p className="mt-1 text-xs text-red-400">{errors.confirmPassword}</p>
          )}
        </div>
        
        <Button 
          type="submit" 
          disabled={loading}
          className="bg-white hover:bg-gray-200 text-black rounded-none font-medium py-2 px-4 w-full md:w-auto"
        >
          {loading ? 'Updating...' : 'Update Password'}
        </Button>
      </form>
    </div>
  )
}