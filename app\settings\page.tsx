'use client'

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'

export default function AccountSettings() {
  const [user, setUser] = useState<{ id: string } | null>(null)
  const [loading, setLoading] = useState(true)
  const [email, setEmail] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  
  const supabase = createClient()
  const router = useRouter()
  
  useEffect(() => {
    const getUser = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error || !session) {
        router.push('/login')
        return
      }
      
      setUser(session.user)
      setEmail(session.user.email || '')
      
      // Fetch profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select('first_name, last_name')
        .eq('id', session.user.id)
        .single()
      
      if (profile) {
        setFirstName(profile.first_name || '')
        setLastName(profile.last_name || '')
      }
      
      setLoading(false)
    }
    
    getUser()
  }, [supabase, router])
  
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setMessage(null)
    
    if (!user) return
    
    try {
      setLoading(true)
      
      // Update profile
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: firstName,
          last_name: lastName,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
      
      if (error) throw error
      
      setMessage({ type: 'success', text: 'Profile updated successfully' })
    } catch (error: unknown) {
      setMessage({ type: 'error', text: error instanceof Error ? error.message : 'Error updating profile' })
    } finally {
      setLoading(false)
    }
  }
  
  if (loading) {
    return <div className="text-center text-white">Loading...</div>
  }
  
  return (
    <div className="max-w-xl mx-auto">
      <h2 className="text-xl font-medium mb-6 text-white">Account Information</h2>
      
      {message && (
        <div 
          className={`p-3 mb-6 ${
            message.type === 'success' ? 'bg-green-900/30 border border-green-800 text-green-200' : 
                                         'bg-red-900/30 border border-red-800 text-red-200'
          }`}
        >
          {message.text}
        </div>
      )}
      
      <form onSubmit={handleUpdateProfile} className="space-y-6">
        <div>
          <Label htmlFor="email" className="text-white">Email address</Label>
          <Input
            id="email"
            type="email"
            value={email}
            disabled
            className="mt-1 bg-neutral-800 border-neutral-700 text-neutral-400 rounded-none"
          />
          <p className="mt-1 text-xs text-neutral-400">Your email address cannot be changed</p>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName" className="text-white">First name</Label>
            <Input
              id="firstName"
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
              placeholder="First name"
            />
          </div>
          <div>
            <Label htmlFor="lastName" className="text-white">Last name</Label>
            <Input
              id="lastName"
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="mt-1 bg-neutral-800 border-neutral-700 text-white rounded-none"
              placeholder="Last name"
            />
          </div>
        </div>
        
        <Button 
          type="submit" 
          disabled={loading}
          className="bg-white hover:bg-gray-200 text-black rounded-none font-medium py-2 px-4 w-full md:w-auto"
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </form>
    </div>
  )
}