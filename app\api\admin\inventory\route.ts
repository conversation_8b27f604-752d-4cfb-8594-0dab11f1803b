import { NextRequest, NextResponse } from 'next/server';
import { requireAdmin } from '@/src/db/auth';
import { getAllInventory, updateInventoryQuantity } from '@/src/db/inventory';

export async function GET() {
  try {
    // Require admin access
    await requireAdmin();

    const inventoryData = await getAllInventory();
    return NextResponse.json({ inventory: inventoryData });
  } catch (error) {
    console.error('Error fetching inventory:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ error: 'Failed to fetch inventory' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Require admin access
    await requireAdmin();

    const { inventoryId, quantity } = await request.json();

    if (!inventoryId || quantity === undefined) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    await updateInventoryQuantity(inventoryId, quantity);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating inventory:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Unauthorized') {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      if (error.message === 'Forbidden: Admin access required') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
    }
    
    return NextResponse.json({ error: 'Failed to update inventory' }, { status: 500 });
  }
}