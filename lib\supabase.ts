// Export Supabase client utilities
import { createClient } from '@/utils/supabase/client'

// Create and export the Supabase client instance
export const supabase = createClient()

// Re-export the client creation functions for flexibility
export { createClient as createClientBrowser } from '@/utils/supabase/client'
export { createClient as createClientServer } from '@/utils/supabase/server'
export { updateSession } from '@/utils/supabase/middleware'

// Default export for convenience
export default supabase
