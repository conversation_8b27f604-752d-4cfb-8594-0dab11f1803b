'use client';

import Script from 'next/script';
import { useEffect, useState } from 'react';

export function SquareScript() {
  const [squareSrc, setSquareSrc] = useState<string>();

  useEffect(() => {
    // This runs only on the client side
    setSquareSrc(
      window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
        ? 'https://sandbox.web.squarecdn.com/v1/square.js'
        : 'https://web.squarecdn.com/v1/square.js'
    );
  }, []);

  if (!squareSrc) return null;

  return (
    <Script 
      id="square-script"
      src={squareSrc}
      strategy="afterInteractive"
      onLoad={() => console.log('Square.js loaded successfully')}
      onError={(e) => console.error('Failed to load Square.js', e)}
    />
  );
}
